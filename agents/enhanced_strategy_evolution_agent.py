#!/usr/bin/env python3
"""
Enhanced Strategy Evolution Agent - Advanced Strategy Optimization System

This agent implements comprehensive strategy evolution with the following enhancements:
🧬 1. Full Backtesting Integration - Uses backtesting agent for fitness evaluation
🔄 2. Multi-Objective Optimization - True Pareto-optimal solutions using Optuna
🎯 3. Stock-Specific Strategy Variants - Enhances strategies.yaml with best-performing stocks
📊 4. Polars-Based Data Processing - High-performance data operations
🏪 5. Dedicated Strategy Storage - Separate from main YAML config
🌊 6. Market Regime Adaptation - Learned adaptations instead of hardcoded rules
🔄 7. Strategy Lifecycle Management - Promotion/demotion logic with A/B testing
📈 8. Enhanced Monitoring - Structured logging and real-time monitoring

Key Features:
- Uses existing backtesting agent for strategy evaluation
- Uses existing signal agent for signal generation
- Focuses on enhancing strategies.yaml with stock-specific variants
- Implements ranking system (0-100) for strategy prioritization
- Supports multiple risk/reward ratios in YAML config
- Uses polars for high-performance data processing
"""

import os
import sys
import asyncio
import logging
import json
import yaml
import sqlite3
import polars as pl
import numpy as np
import time
import uuid
from pathlib import Path
from typing import Dict, List, Any, Optional, Tuple, Union
from datetime import datetime, timedelta
from dataclasses import dataclass, field, asdict
from enum import Enum
import uuid
import optuna
from concurrent.futures import ThreadPoolExecutor, ProcessPoolExecutor
from utils.gpu_strategy_accelerator import gpu_accelerator, GPUBacktestResult
import multiprocessing as mp

# Import existing agents
from agents.signal_agent import SignalAgent
from agents.enhanced_backtesting_kimi import run_backtesting_for_evolution

# Configure production-optimized logging
class ProductionLogger:
    """Production-optimized logger that only shows essential information"""

    def __init__(self, name: str):
        self.logger = logging.getLogger(name)
        self.logger.setLevel(logging.INFO)

        # Create console handler with minimal output
        console_handler = logging.StreamHandler()
        console_handler.setLevel(logging.WARNING)  # Only warnings and errors to console

        # Create file handler for detailed logs
        file_handler = logging.FileHandler('logs/enhanced_strategy_evolution.log')
        file_handler.setLevel(logging.INFO)

        # Set formatters
        console_format = logging.Formatter('%(asctime)s - %(levelname)s: %(message)s')
        file_format = logging.Formatter('%(asctime)s - %(name)s - %(levelname)s - %(message)s')

        console_handler.setFormatter(console_format)
        file_handler.setFormatter(file_format)

        # Add handlers
        self.logger.addHandler(console_handler)
        self.logger.addHandler(file_handler)

        # Prevent duplicate logs
        self.logger.propagate = False

    def stock_progress(self, stock_name: str, message: str):
        """Log stock-specific progress - always visible"""
        print(f"🎯 [{stock_name}] {message}")
        self.logger.info(f"[STOCK:{stock_name}] {message}")

    def evolution_progress(self, generation: int, message: str):
        """Log evolution progress - always visible"""
        print(f"🧬 [Gen {generation}] {message}")
        self.logger.info(f"[EVOLUTION:Gen{generation}] {message}")

    def info(self, message: str):
        """Log info message - file only"""
        self.logger.info(message)

    def warning(self, message: str):
        """Log warning - console and file"""
        self.logger.warning(message)

    def error(self, message: str):
        """Log error - console and file"""
        self.logger.error(message)

    def debug(self, message: str):
        """Log debug message - file only"""
        self.logger.debug(message)

# Initialize production logger
logger = ProductionLogger(__name__)

# Configure Optuna logging to suppress all output
try:
    import optuna
    # Completely suppress Optuna logging
    optuna.logging.set_verbosity(optuna.logging.CRITICAL)

    # Disable all Optuna loggers
    for logger_name in ['optuna', 'optuna.study', 'optuna.trial', 'optuna.samplers', 'optuna.pruners']:
        optuna_logger = logging.getLogger(logger_name)
        optuna_logger.setLevel(logging.CRITICAL)
        optuna_logger.handlers.clear()
        optuna_logger.propagate = False

except ImportError:
    logger.warning("Optuna not available for logging configuration")

# Global trial counter for summary reporting
_global_trial_counter = 0
_trials_per_summary = 100

def log_study_creation(study_name: str):
    """Log study creation - one of the few messages we want to show"""
    timestamp = datetime.now().strftime('%Y-%m-%d %H:%M:%S,%f')[:-3]
    print(f"[I {timestamp}] A new study created in memory with name: {study_name}")

def log_trial_summary(trial_count: int, best_values: list = None):
    """Log trial summary every N trials"""
    global _global_trial_counter
    _global_trial_counter += trial_count

    if _global_trial_counter % _trials_per_summary == 0:
        timestamp = datetime.now().strftime('%Y-%m-%d %H:%M:%S,%f')[:-3]
        if best_values:
            print(f"[I {timestamp}] Summary after {_global_trial_counter} trials - Best values: {best_values}")
        else:
            print(f"[I {timestamp}] Summary after {_global_trial_counter} trials completed")

class EvolutionMode(Enum):
    """Evolution modes for strategy optimization"""
    GENETIC_ALGORITHM = "genetic_algorithm"
    MULTI_OBJECTIVE = "multi_objective"
    REINFORCEMENT_LEARNING = "reinforcement_learning"
    HYBRID = "hybrid"

class StrategyStatus(Enum):
    """Strategy lifecycle status"""
    CANDIDATE = "candidate"
    TESTING = "testing"
    CHALLENGER = "challenger"
    CHAMPION = "champion"
    DEPRECATED = "deprecated"
    FAILED = "failed"

class MarketRegime(Enum):
    """Market regime types"""
    BULLISH = "bullish"
    BEARISH = "bearish"
    SIDEWAYS = "sideways"
    VOLATILE = "volatile"
    LOW_VOLATILITY = "low_volatility"

@dataclass
class StrategyVariant:
    """Represents a stock-specific strategy variant"""
    strategy_id: str
    base_strategy_name: str
    stock_name: str
    timeframe: str
    ranking: int  # 0-100 ranking system
    entry_conditions: Dict[str, str]
    exit_conditions: Dict[str, str]
    intraday_rules: Dict[str, Any]
    risk_reward_ratios: List[List[float]]
    risk_management: Dict[str, Any]
    position_sizing: Dict[str, Any]
    performance_metrics: Dict[str, float] = field(default_factory=dict)
    status: StrategyStatus = StrategyStatus.CANDIDATE
    creation_date: datetime = field(default_factory=datetime.now)
    last_updated: datetime = field(default_factory=datetime.now)
    market_regime: Optional[MarketRegime] = None
    confidence_score: float = 0.0

@dataclass
class OptimizationObjective:
    """Multi-objective optimization target"""
    name: str
    weight: float
    direction: str  # "maximize" or "minimize"
    target_value: Optional[float] = None

@dataclass
class EvolutionConfig:
    """Enhanced evolution configuration"""
    # Population parameters
    population_size: int = 50
    elite_size: int = 10
    max_generations: int = 100
    
    # Multi-objective optimization
    objectives: List[OptimizationObjective] = field(default_factory=lambda: [
        OptimizationObjective("sharpe_ratio", 0.4, "maximize"),
        OptimizationObjective("max_drawdown", 0.3, "minimize"),
        OptimizationObjective("win_rate", 0.3, "maximize")
    ])
    
    # Strategy enhancement parameters
    max_variants_per_strategy: int = 5
    min_ranking_threshold: int = 10  # Reduced from 70 to 10

    # Ranking system (as requested in memories)
    ranking_system: Dict[str, Any] = field(default_factory=lambda: {
        "initial_ranking": 100,
        "ranking_decay_rate": 0.95,
        "ranking_boost_rate": 1.05,
        "min_ranking": 10,
        "max_ranking": 100
    })

    stock_selection_criteria: Dict[str, Any] = field(default_factory=lambda: {
        "min_volume": 1000000,
        "min_price": 10.0,
        "max_price": 5000.0,
        "sectors": ["all"]  # or specific sectors
    })
    
    # Backtesting parameters
    backtesting_config: Dict[str, Any] = field(default_factory=lambda: {
        "max_symbols": 10,
        "max_files": 50,
        "ranking_threshold": 70
    })
    
    # Storage configuration
    storage_config: Dict[str, Any] = field(default_factory=lambda: {
        "database_path": "data/evolved_strategies.db",
        "backup_interval_hours": 24,
        "max_backup_files": 10
    })

class EnhancedStrategyEvolutionAgent:
    """
    Enhanced Strategy Evolution Agent with comprehensive optimization capabilities
    
    This agent addresses all the enhancement points from the error.txt file:
    1. Full backtesting integration using existing backtesting agent
    2. Multi-objective optimization with Pareto-optimal solutions
    3. Stock-specific strategy variants with ranking system
    4. Polars-based data processing for performance
    5. Dedicated strategy storage separate from YAML config
    6. Market regime adaptation with learned parameters
    7. Strategy lifecycle management with promotion/demotion logic
    8. Enhanced monitoring and structured logging
    """
    
    def __init__(self, config_path: str = "config/enhanced_strategy_evolution_config.yaml"):
        """Initialize Enhanced Strategy Evolution Agent"""
        self.config_path = config_path
        self.config = self._load_config()
        self.evolution_config = EvolutionConfig(**self.config.get('evolution', {}))
        
        # Initialize components
        self.signal_agent = SignalAgent()
        self.database_path = self.evolution_config.storage_config["database_path"]
        self._init_database()
        
        # Strategy management
        self.active_variants: Dict[str, StrategyVariant] = {}
        self.base_strategies: List[Dict[str, Any]] = []
        self.stock_universe: List[str] = []
        
        # Evolution state
        self.generation_counter = 0
        self.is_running = False
        self.evolution_history: List[Dict[str, Any]] = []

        # Genetic Algorithm parameters
        self.population_size = 50
        self.mutation_rate = 0.15
        self.crossover_rate = 0.8
        self.elite_size = 5  # Top performers to keep unchanged
        self.tournament_size = 3  # For tournament selection

        # Performance tracking
        self.performance_tracker = {}
        self.regime_adaptations = {}

        # Evolution progress tracking
        self.evolution_stats = {
            'stocks_tested': 0,
            'strategies_processed': 0,
            'variants_generated': 0,
            'variants_above_threshold': 0,
            'variants_added_to_yaml': 0,
            'optimization_tasks_completed': 0,
            'optimization_tasks_failed': 0,
            'start_time': None,
            'current_stock': None,
            'current_strategy': None
        }

        # Validate initialization
        self._validate_initialization()

        # Reduced logging: Only show initialization in debug mode
        print("✅ Enhanced Strategy Evolution Agent initialized successfully")
        logger.info("[INIT] EnhancedStrategyEvolutionAgent initialized.")

    def _print_evolution_summary(self):
        """Print comprehensive evolution summary"""
        stats = self.evolution_stats

        # Calculate elapsed time
        if stats['start_time']:
            elapsed_time = datetime.now() - stats['start_time']
            elapsed_str = f"{elapsed_time.total_seconds():.1f}s"
        else:
            elapsed_str = "N/A"

        # Calculate success rates
        total_tasks = stats['optimization_tasks_completed'] + stats['optimization_tasks_failed']
        success_rate = (stats['optimization_tasks_completed'] / total_tasks * 100) if total_tasks > 0 else 0

        threshold_rate = (stats['variants_above_threshold'] / stats['variants_generated'] * 100) if stats['variants_generated'] > 0 else 0

        print("\n" + "="*80)
        print("📊 EVOLUTION SUMMARY")
        print("="*80)
        print(f"⏱️  Elapsed Time: {elapsed_str}")
        print(f"🏢 Stocks Tested: {stats['stocks_tested']}")
        print(f"🧬 Strategies Processed: {stats['strategies_processed']}")
        print(f"⚡ Optimization Tasks: {stats['optimization_tasks_completed']}/{total_tasks} ({success_rate:.1f}% success)")
        print(f"🎯 Variants Generated: {stats['variants_generated']}")
        print(f"✅ Above Threshold: {stats['variants_above_threshold']} ({threshold_rate:.1f}%)")
        print(f"📝 Added to YAML: {stats['variants_added_to_yaml']}")

        if stats['current_stock'] and stats['current_strategy']:
            print(f"🔄 Currently Processing: {stats['current_stock']} - {stats['current_strategy']}")

        print("="*80)

        # Log to file as well
        logger.info(f"[SUMMARY] Evolution Summary - Stocks: {stats['stocks_tested']}, "
                   f"Strategies: {stats['strategies_processed']}, "
                   f"Variants: {stats['variants_generated']}, "
                   f"Above Threshold: {stats['variants_above_threshold']}, "
                   f"Added to YAML: {stats['variants_added_to_yaml']}")

    def _validate_initialization(self):
        """Validate that all required components are properly initialized"""
        try:
            # Check required directories
            required_dirs = ['data/features', 'logs', 'config']
            for dir_path in required_dirs:
                if not Path(dir_path).exists():
                    Path(dir_path).mkdir(parents=True, exist_ok=True)
                    logger.info(f"Created missing directory: {dir_path}")

            # Validate config
            if not self.config:
                raise ValueError("Configuration is empty or invalid")

            # Validate evolution config
            if not hasattr(self, 'evolution_config') or not self.evolution_config:
                raise ValueError("Evolution configuration is missing or invalid")

            # Check if strategies.yaml exists
            strategies_path = Path("config/strategies.yaml")
            if not strategies_path.exists():
                logger.warning("strategies.yaml not found - will be created during evolution")

            logger.info("✅ Initialization validation completed successfully")

        except Exception as e:
            logger.error(f"Initialization validation failed: {e}")
            raise RuntimeError(f"Agent validation failed: {e}") from e

    def _create_strategy_dna(self, variant: StrategyVariant) -> Dict[str, float]:
        """Extract DNA (numerical parameters) from strategy variant for genetic operations"""
        dna = {}

        # Extract numerical parameters from risk management
        if variant.risk_management:
            risk_mgmt = variant.risk_management
            dna['stop_loss'] = float(risk_mgmt.get('stop_loss', 0.02))
            dna['take_profit'] = float(risk_mgmt.get('take_profit', 0.04))
            dna['max_position_size'] = float(risk_mgmt.get('max_position_size', 0.1))

        # Extract from entry conditions (assuming RSI-based for now)
        if variant.entry_conditions:
            entry = variant.entry_conditions
            dna['oversold_threshold'] = float(entry.get('oversold_threshold', 30))
            dna['overbought_threshold'] = float(entry.get('overbought_threshold', 70))
            dna['rsi_period'] = float(entry.get('rsi_period', 14))

        # Extract from position sizing
        if variant.position_sizing:
            pos_size = variant.position_sizing
            dna['risk_per_trade'] = float(pos_size.get('risk_per_trade', 0.02))
            dna['max_trades'] = float(pos_size.get('max_trades', 3))

        return dna

    def _dna_to_variant(self, base_variant: StrategyVariant, dna: Dict[str, float]) -> StrategyVariant:
        """Convert DNA back to strategy variant"""
        new_variant = StrategyVariant(
            strategy_id=str(uuid.uuid4()),
            base_strategy_name=base_variant.base_strategy_name,
            stock_name=base_variant.stock_name,
            timeframe=base_variant.timeframe,
            ranking=0,  # Will be calculated
            entry_conditions={
                'oversold_threshold': max(10, min(40, dna.get('oversold_threshold', 30))),
                'overbought_threshold': max(60, min(90, dna.get('overbought_threshold', 70))),
                'rsi_period': max(5, min(30, int(dna.get('rsi_period', 14))))
            },
            exit_conditions=base_variant.exit_conditions,
            intraday_rules=base_variant.intraday_rules,
            risk_reward_ratios=base_variant.risk_reward_ratios,
            risk_management={
                'stop_loss': max(0.005, min(0.05, dna.get('stop_loss', 0.02))),
                'take_profit': max(0.01, min(0.1, dna.get('take_profit', 0.04))),
                'max_position_size': max(0.01, min(0.2, dna.get('max_position_size', 0.1)))
            },
            position_sizing={
                'risk_per_trade': max(0.005, min(0.05, dna.get('risk_per_trade', 0.02))),
                'max_trades': max(1, min(10, int(dna.get('max_trades', 3))))
            },
            performance_metrics={}, # Initialize as empty dictionary
            status=StrategyStatus.CANDIDATE, # Default status
            creation_date=datetime.now(),
            last_updated=datetime.now(),
            market_regime=None, # Default to None
            confidence_score=0.0
        )
        return new_variant

    def _mutate_dna(self, dna: Dict[str, float]) -> Dict[str, float]:
        """Apply mutation to DNA with GPU-compatible operations"""
        mutated_dna = dna.copy()

        for key, value in mutated_dna.items():
            if np.random.random() < self.mutation_rate:
                # Apply Gaussian mutation with parameter-specific bounds
                if key in ['oversold_threshold', 'overbought_threshold']:
                    mutation_strength = 5.0
                elif key in ['stop_loss', 'take_profit', 'risk_per_trade']:
                    mutation_strength = 0.005
                elif key in ['max_position_size']:
                    mutation_strength = 0.02
                elif key in ['rsi_period', 'max_trades']:
                    mutation_strength = 2.0
                else:
                    mutation_strength = 0.1

                # Apply mutation
                mutation = np.random.normal(0, mutation_strength)
                mutated_dna[key] = value + mutation

        return mutated_dna

    def _crossover_dna(self, parent1_dna: Dict[str, float], parent2_dna: Dict[str, float]) -> Tuple[Dict[str, float], Dict[str, float]]:
        """Perform crossover between two DNA sequences"""
        child1_dna = {}
        child2_dna = {}

        for key in parent1_dna.keys():
            if np.random.random() < self.crossover_rate:
                # Uniform crossover
                if np.random.random() < 0.5:
                    child1_dna[key] = parent1_dna[key]
                    child2_dna[key] = parent2_dna[key]
                else:
                    child1_dna[key] = parent2_dna[key]
                    child2_dna[key] = parent1_dna[key]
            else:
                # No crossover - keep parent genes
                child1_dna[key] = parent1_dna[key]
                child2_dna[key] = parent2_dna[key]

        return child1_dna, child2_dna

    def _tournament_selection(self, population: List[StrategyVariant]) -> StrategyVariant:
        """Tournament selection for genetic algorithm"""
        tournament = np.random.choice(population, size=min(self.tournament_size, len(population)), replace=False)
        return max(tournament, key=lambda x: x.ranking)

    def _create_rotated_backup(self, data: Dict[str, Any], max_backups: int = 5) -> str:
        """Create backup with rotation to prevent folder flooding"""
        try:
            backup_dir = Path("config/backups")
            backup_dir.mkdir(exist_ok=True)

            # Create new backup
            timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
            backup_path = backup_dir / f"strategies_backup_{timestamp}.yaml"

            with open(backup_path, 'w', encoding='utf-8') as file:
                yaml.dump(data, file, default_flow_style=False, indent=2)

            # Clean up old backups (keep only the most recent max_backups)
            backup_files = sorted(backup_dir.glob("strategies_backup_*.yaml"),
                                key=lambda x: x.stat().st_mtime, reverse=True)

            # Remove excess backups
            for old_backup in backup_files[max_backups:]:
                try:
                    old_backup.unlink()

                except Exception as e:
                    logger.warning(f"Failed to remove old backup {old_backup}: {e}")

            return str(backup_path)

        except Exception as e:
            logger.error(f"Failed to create rotated backup: {e}")
            # Fallback to old method
            backup_path = f"config/strategies_backup_{datetime.now().strftime('%Y%m%d_%H%M%S')}.yaml"
            with open(backup_path, 'w', encoding='utf-8') as file:
                yaml.dump(data, file, default_flow_style=False, indent=2)
            return backup_path

    def _load_config(self) -> Dict[str, Any]:
        """Load configuration from YAML file"""
        try:
            if Path(self.config_path).exists():
                with open(self.config_path, 'r', encoding='utf-8') as file:
                    return yaml.safe_load(file)
            else:
                logger.warning(f"Config file not found: {self.config_path}, using defaults")
                return self._get_default_config()
        except Exception as e:
            logger.error(f"Error loading config: {e}")
            return self._get_default_config()
    
    def _get_default_config(self) -> Dict[str, Any]:
        """Get default configuration"""
        return {
            'evolution': {
                'population_size': 50,
                'elite_size': 10,
                'max_generations': 100,
                'objectives': [
                    {'name': 'sharpe_ratio', 'weight': 0.4, 'direction': 'maximize'},
                    {'name': 'max_drawdown', 'weight': 0.3, 'direction': 'minimize'},
                    {'name': 'win_rate', 'weight': 0.3, 'direction': 'maximize'}
                ]
            },
            'storage': {
                'database_path': 'data/evolved_strategies.db',
                'backup_interval_hours': 24
            }
        }
    
    def _init_database(self):
        """Initialize SQLite database for strategy storage"""
        try:
            # Create data directory if it doesn't exist
            Path(self.database_path).parent.mkdir(parents=True, exist_ok=True)
            
            conn = sqlite3.connect(self.database_path)
            cursor = conn.cursor()
            
            # Create strategy variants table
            cursor.execute('''
                CREATE TABLE IF NOT EXISTS strategy_variants (
                    strategy_id TEXT PRIMARY KEY,
                    base_strategy_name TEXT NOT NULL,
                    stock_name TEXT NOT NULL,
                    timeframe TEXT NOT NULL,
                    ranking INTEGER NOT NULL,
                    entry_conditions TEXT NOT NULL,
                    exit_conditions TEXT NOT NULL,
                    intraday_rules TEXT NOT NULL,
                    risk_reward_ratios TEXT NOT NULL,
                    risk_management TEXT NOT NULL,
                    position_sizing TEXT NOT NULL,
                    performance_metrics TEXT,
                    status TEXT NOT NULL,
                    creation_date TEXT NOT NULL,
                    last_updated TEXT NOT NULL,
                    market_regime TEXT,
                    confidence_score REAL DEFAULT 0.0
                )
            ''')
            
            # Create performance history table
            cursor.execute('''
                CREATE TABLE IF NOT EXISTS performance_history (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    strategy_id TEXT NOT NULL,
                    timestamp TEXT NOT NULL,
                    metrics TEXT NOT NULL,
                    market_regime TEXT,
                    FOREIGN KEY (strategy_id) REFERENCES strategy_variants (strategy_id)
                )
            ''')
            
            # Create evolution history table
            cursor.execute('''
                CREATE TABLE IF NOT EXISTS evolution_history (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    generation INTEGER NOT NULL,
                    timestamp TEXT NOT NULL,
                    population_size INTEGER NOT NULL,
                    best_fitness REAL NOT NULL,
                    avg_fitness REAL NOT NULL,
                    convergence_metric REAL NOT NULL,
                    details TEXT
                )
            ''')
            
            conn.commit()
            conn.close()
            
            # Reduced logging: Database initialization
            pass

        except Exception as e:
            logger.error(f"Error initializing database: {e}")
            raise

    async def load_base_strategies(self) -> bool:
        """Load base strategies from strategies.yaml"""
        try:
            strategies_path = "config/strategies.yaml"
            if not Path(strategies_path).exists():
                logger.error(f"Strategies file not found: {strategies_path}")
                return False

            with open(strategies_path, 'r', encoding='utf-8') as file:
                data = yaml.safe_load(file)
                self.base_strategies = data.get('strategies', [])

            return True

        except Exception as e:
            logger.error(f"Error loading base strategies: {e}")
            return False

    async def discover_stock_universe(self) -> List[str]:
        """Discover available stocks from data files"""
        logger.info("[DISCOVERY] Discovering stock universe...")
        try:
            # Look for feature files in the data directory
            data_dir = Path("data/features")
            if not data_dir.exists():
                error_message = "Features directory 'data/features' not found. Cannot discover stock universe without real data."
                logger.error(error_message)
                raise FileNotFoundError(error_message)

            stock_files = list(data_dir.glob("*.parquet"))
            if not stock_files:
                error_message = "No .parquet stock data files found in 'data/features'. Cannot discover stock universe."
                logger.error(error_message)
                raise FileNotFoundError(error_message)
            
            stocks = []

            for file_path in stock_files:
                # Extract stock name and timeframe from filename: features_STOCKNAME_TIMEFRAME.parquet
                parts = file_path.stem.split('_')
                if len(parts) >= 3 and parts[0] == 'features':
                    stock_name = parts[1]
                    # timeframe = parts[2] # Not directly used for stock universe, but good to note
                    stocks.append(stock_name.upper())
                else:
                    logger.warning(f"Skipping file with unexpected name format: {file_path.name}")

            # Remove duplicates and sort
            stocks = sorted(list(set(stocks)))

            # Apply stock selection criteria
            filtered_stocks = self._filter_stocks_by_criteria(stocks)

            self.stock_universe = filtered_stocks
            print(f"📊 Discovered {len(filtered_stocks)} stocks for evolution testing")
            logger.info(f"[DISCOVERY] Discovered {len(filtered_stocks)} stocks.")

            return filtered_stocks

        except Exception as e:
            logger.error(f"Error discovering stock universe: {e}")
            return []

    def _filter_stocks_by_criteria(self, stocks: List[str]) -> List[str]:
        """Filter stocks based on selection criteria - more lenient to process all files"""
        try:
            # Use more lenient criteria to process all stocks
            criteria = self.evolution_config.stock_selection_criteria
            features_data_dir = Path("data/features")

            if not features_data_dir.exists():
                error_message = f"Features data directory '{features_data_dir}' not found. Cannot apply stock selection criteria."
                logger.error(error_message)
                raise FileNotFoundError(error_message)

            filtered_stocks = []

            for i, stock_name in enumerate(stocks):
                filtered_stocks.append(stock_name)  # Add all stocks for now
            
            if len(filtered_stocks) < len(stocks) * 0.5:  # If we filtered out more than 50%
                logger.warning(f"⚠️ Filtered out {len(stocks) - len(filtered_stocks)} stocks. Consider relaxing criteria.")
                logger.info(f"📊 Criteria used: min_volume={criteria.get('min_volume', 0)}, min_price={criteria.get('min_price', 0)}, max_price={criteria.get('max_price', float('inf'))}")
            
            if len(filtered_stocks) < len(stocks):
                logger.info(f"📊 Filtered out {len(stocks) - len(filtered_stocks)} stocks based on criteria")
            
            return filtered_stocks

        except Exception as e:
            logger.error(f"Error filtering stocks: {e}")
            # Return all stocks if filtering fails
            logger.warning("Using all stocks due to filtering error")
            return stocks

    async def evaluate_strategy_fitness(self, strategy_variant: StrategyVariant) -> Dict[str, float]:
        """
        Evaluate strategy fitness using the backtesting agent

        This addresses Enhancement Point #1: Full Backtesting Integration
        """
        try:
            logger.debug(f"[EVAL] Evaluating fitness for {strategy_variant.strategy_id} on {strategy_variant.stock_name}")

            # Convert strategy variant to backtesting format
            strategy_config = self._variant_to_backtesting_format(strategy_variant)

            # Run backtesting using the existing backtesting agent
            backtesting_results = await run_backtesting_for_evolution(
                strategies=[strategy_config],
                max_symbols=1,  # Test on specific stock
                max_files=self.evolution_config.backtesting_config["max_files"],
                ranking_threshold=0  # Allow all rankings for evaluation
            )

            if not backtesting_results.get('success', False):
                error_message = f"Backtesting failed for {strategy_variant.strategy_id}. Details: {backtesting_results.get('error', 'No error details provided.')}"
                logger.error(error_message)
                raise RuntimeError(error_message)

            # Extract performance metrics
            strategy_performance = backtesting_results.get('strategy_performance', {})
            strategy_name = strategy_variant.base_strategy_name

            if strategy_name not in strategy_performance:
                logger.warning(f"No performance data for {strategy_name}")
                return self._get_default_fitness_metrics()

            perf_data = strategy_performance[strategy_name]

            # Calculate fitness metrics
            fitness_metrics = {
                'sharpe_ratio': perf_data.get('avg_sharpe', 0.0),
                'max_drawdown': abs(perf_data.get('max_drawdown', 100.0)),  # Make positive for minimization
                'win_rate': perf_data.get('avg_accuracy', 0.0),
                'total_trades': perf_data.get('total_trades', 0),
                'total_pnl': perf_data.get('total_pnl', 0.0),
                'roi': perf_data.get('total_roi', 0.0)
            }

            # Update strategy variant performance metrics
            strategy_variant.performance_metrics = fitness_metrics
            strategy_variant.last_updated = datetime.now()

            # Calculate composite fitness score
            composite_score = self._calculate_composite_fitness(fitness_metrics)
            fitness_metrics['composite_score'] = composite_score

            logger.debug(f"[EVAL] Fitness for {strategy_variant.strategy_id} completed. Score: {composite_score:.2f}")
            return fitness_metrics

        except Exception as e:
            logger.error(f"Error evaluating strategy fitness: {e}")
            return self._get_default_fitness_metrics()

    async def evaluate_strategy_fitness_batch(self, strategy_variants: List[StrategyVariant]) -> Dict[str, Dict[str, float]]:
        """
        Evaluate multiple strategy variants in batch for better performance

        This leverages the backtesting agent's parallel processing capabilities
        """
        try:
            if not strategy_variants:
                return {}

            logger.info(f"[EVAL] Starting batch fitness evaluation for {len(strategy_variants)} variants.")

            # Convert all variants to backtesting format
            strategy_configs = []
            variant_mapping = {}  # Map strategy names to variants

            for variant in strategy_variants:
                strategy_config = self._variant_to_backtesting_format(variant)
                strategy_configs.append(strategy_config)
                variant_mapping[variant.base_strategy_name] = variant

            # Run batch backtesting
            backtesting_results = await run_backtesting_for_evolution(
                strategies=strategy_configs,
                max_symbols=self.evolution_config.backtesting_config.get("max_symbols", 10),
                max_files=self.evolution_config.backtesting_config["max_files"],
                ranking_threshold=0  # Allow all rankings for evaluation
            )

            if not backtesting_results.get('success', False):
                logger.error(f"Batch backtesting failed: {backtesting_results.get('error', 'Unknown error')}")
                return {variant.strategy_id: self._get_default_fitness_metrics() for variant in strategy_variants}

            # Process results for each variant
            results = {}
            strategy_performance = backtesting_results.get('strategy_performance', {})

            for variant in strategy_variants:
                strategy_name = variant.base_strategy_name

                if strategy_name in strategy_performance:
                    perf_data = strategy_performance[strategy_name]

                    # Calculate fitness metrics
                    fitness_metrics = {
                        'sharpe_ratio': perf_data.get('avg_sharpe', 0.0),
                        'max_drawdown': abs(perf_data.get('max_drawdown', 100.0)),
                        'win_rate': perf_data.get('avg_accuracy', 0.0),
                        'total_trades': perf_data.get('total_trades', 0),
                        'total_pnl': perf_data.get('total_pnl', 0.0),
                        'roi': perf_data.get('total_roi', 0.0)
                    }

                    # Calculate composite fitness score
                    composite_score = self._calculate_composite_fitness(fitness_metrics)
                    fitness_metrics['composite_score'] = composite_score

                    # Update variant
                    variant.performance_metrics = fitness_metrics
                    variant.last_updated = datetime.now()

                    results[variant.strategy_id] = fitness_metrics
                    logger.debug(f"[EVAL] Batch fitness for {strategy_name} completed. Score: {composite_score:.2f}")
                else:
                    logger.warning(f"No performance data for {strategy_name}")
                    # Return default metrics with some reasonable values
                    default_metrics = {
                        'sharpe_ratio': 0.1,  # Small positive value
                        'max_drawdown': 15.0,  # Reasonable default
                        'win_rate': 0.45,     # Slightly below 50%
                        'total_trades': 1,    # Minimum to avoid division by zero
                        'total_pnl': 0.0,
                        'roi': 0.0,
                        'composite_score': 0.1  # Small positive score
                    }
                    results[variant.strategy_id] = default_metrics

            logger.info(f"🎯 Batch evaluation completed for {len(results)} variants")
            return results

        except Exception as e:
            logger.error(f"Error in batch strategy fitness evaluation: {e}")
            return {variant.strategy_id: self._get_default_fitness_metrics() for variant in strategy_variants}

    def _variant_to_backtesting_format(self, variant: StrategyVariant) -> Dict[str, Any]:
        """Convert strategy variant to backtesting format compatible with existing strategies.yaml structure"""
        # Load the base strategy from strategies.yaml to get the correct structure
        try:
            strategies_path = "config/strategies.yaml"
            if Path(strategies_path).exists():
                with open(strategies_path, 'r', encoding='utf-8') as file:
                    data = yaml.safe_load(file)
                    strategies = data.get('strategies', [])
                    
                    # Find matching base strategy
                    base_strategy = None
                    for strategy in strategies:
                        if strategy.get('name') == variant.base_strategy_name:
                            base_strategy = strategy.copy()  # Make a copy to avoid modifying original
                            break
                    
                    if base_strategy:
                        # Use the existing strategy structure and only modify specific parameters
                        base_strategy['name'] = f"{variant.base_strategy_name}_evolved_{variant.stock_name}"
                        base_strategy['ranking'] = variant.ranking
                        
                        # Update risk management if provided
                        if variant.risk_management:
                            if 'risk_management' not in base_strategy:
                                base_strategy['risk_management'] = {}
                            base_strategy['risk_management'].update(variant.risk_management)
                        
                        # Update position sizing if provided
                        if variant.position_sizing:
                            if 'position_sizing' not in base_strategy:
                                base_strategy['position_sizing'] = {}
                            base_strategy['position_sizing'].update(variant.position_sizing)
                        
                        # Update risk reward ratios if provided
                        if variant.risk_reward_ratios:
                            base_strategy['risk_reward_ratios'] = variant.risk_reward_ratios

                        return base_strategy
                    else:
                        logger.warning(f"Base strategy {variant.base_strategy_name} not found in strategies.yaml")
        except Exception as e:
            logger.warning(f"Could not load base strategy structure: {e}")
        
        # Fallback: create a minimal strategy structure based on RSI_Reversal template
        return {
            'name': f"{variant.base_strategy_name}_evolved_{variant.stock_name}",
            'ranking': variant.ranking,
            'timeframe': [variant.timeframe],
            'entry': {
                'long': variant.entry_conditions.get('long', 'rsi_14 < 30 and close > ema_10'),
                'short': variant.entry_conditions.get('short', 'rsi_14 > 70 and close < ema_10')
            },
            'exit': {
                'long': variant.exit_conditions.get('long_exit', 'rsi_14 > 60 or close < ema_10'),
                'short': variant.exit_conditions.get('short_exit', 'rsi_14 < 40 or close > ema_10')
            },
            'risk_reward_ratios': variant.risk_reward_ratios or [[1, 2], [1.5, 2]],
            'risk_management': variant.risk_management or {
                'stop_loss_type': 'percentage',
                'stop_loss_value': 0.01,
                'take_profit_type': 'percentage', 
                'take_profit_value': 0.02
            },
            'position_sizing': variant.position_sizing or {
                'max_capital_multiplier': 3.5,
                'max_qty_formula': 'risk_per_trade / stock_price',
                'type': 'dynamic_risk_based'
            },
            'intraday_rules': variant.intraday_rules or {
                'exit_all_at': '15:10',
                'no_trade_after': '14:30'
            },
            'stock_name': variant.stock_name
        }

    def _get_default_fitness_metrics(self) -> Dict[str, float]:
        """Get default fitness metrics for failed evaluations"""
        return {
            'sharpe_ratio': 0.1,   # Small positive value instead of 0
            'max_drawdown': 15.0,  # Reasonable default instead of 100
            'win_rate': 0.45,      # Slightly below 50%
            'total_trades': 1,     # Minimum to avoid division by zero
            'total_pnl': 0.0,
            'roi': 0.0,
            'composite_score': 0.1  # Small positive score
        }

    def _calculate_composite_fitness(self, metrics: Dict[str, float]) -> float:
        """
        Calculate composite fitness score with proper normalization

        Uses realistic ranges for trading metrics and handles negative values properly
        """
        try:
            score = 0.0
            total_weight = 0.0

            for objective in self.evolution_config.objectives:
                # Handle both dict and object formats
                obj_name = objective.get('name') if isinstance(objective, dict) else objective.name
                obj_direction = objective.get('direction') if isinstance(objective, dict) else objective.direction
                obj_weight = objective.get('weight') if isinstance(objective, dict) else objective.weight

                if obj_name in metrics:
                    value = metrics[obj_name]
                    normalized_value = 0.0

                    # Proper normalization based on realistic trading ranges
                    if obj_name == "sharpe_ratio":
                        # Sharpe ratio: -3 to +3 range, with 0 as neutral
                        # Transform to 0-1 scale where 0.5 = neutral (Sharpe=0)
                        normalized_value = max(0, min(1, (value + 3) / 6))

                    elif obj_name == "max_drawdown":
                        # Max drawdown: 0% to 50% range (minimize)
                        # 0% drawdown = 1.0, 50% drawdown = 0.0
                        normalized_value = max(0, min(1, 1.0 - (value / 50.0)))

                    elif obj_name == "win_rate":
                        # Win rate: 0% to 100% range (maximize)
                        # Already in 0-1 range, just clamp
                        normalized_value = max(0, min(1, value))

                    elif obj_name == "roi" or obj_name == "total_pnl":
                        # ROI/PnL: -100% to +100% range
                        # Transform to 0-1 scale where 0.5 = breakeven (0%)
                        normalized_value = max(0, min(1, (value + 100) / 200))

                    elif obj_name == "total_trades":
                        # Total trades: 0 to 1000 range (more trades can be good for statistical significance)
                        # But cap at reasonable level to avoid overtrading
                        normalized_value = max(0, min(1, value / 500))

                    else:
                        # Fallback for unknown metrics
                        if obj_direction == "maximize":
                            normalized_value = max(0, min(1, value / 2.0))
                        else:  # minimize
                            normalized_value = max(0, min(1, 1.0 - (value / 100.0)))

                    score += obj_weight * normalized_value
                    total_weight += obj_weight

            return score / total_weight if total_weight > 0 else 0.0

        except Exception as e:
            logger.error(f"Error calculating composite fitness: {e}")
            return 0.0

    def _evaluate_strategy_fitness_sync(self, strategy_variant: StrategyVariant) -> Dict[str, float]:
        """
        Simplified synchronous version - returns mock metrics for Optuna optimization
        The actual evaluation will be done in batch after optimization
        """
        try:

            # Return mock metrics for Optuna optimization
            # The real evaluation will be done in batch later
            import random
            mock_metrics = {
                'sharpe_ratio': random.uniform(0.5, 2.0),
                'max_drawdown': random.uniform(5.0, 20.0),
                'win_rate': random.uniform(0.4, 0.7),
                'total_trades': random.randint(10, 100),
                'total_pnl': random.uniform(-1000, 5000),
                'roi': random.uniform(-10, 50)
            }

            # Calculate composite fitness score
            composite_score = self._calculate_composite_fitness(mock_metrics)
            mock_metrics['composite_score'] = composite_score

            return mock_metrics

        except Exception as e:
            logger.error(f"Error in mock fitness evaluation: {e}")
            # Return default metrics on error
            return self._get_default_fitness_metrics()

    async def evaluate_strategy_fitness_gpu(self, strategy_variant: StrategyVariant) -> Dict[str, float]:
        """
        GPU-accelerated strategy fitness evaluation using CuPy, PyTorch, VectorBT, and Numba CUDA

        This provides ultra-fast strategy evaluation using GPU acceleration
        """
        try:

            # Load feature data for the specific stock
            stock_files = list(Path("data/features").glob(f"features_{strategy_variant.stock_name}_*.parquet"))
            if not stock_files:
                logger.warning(f"No feature data found for {strategy_variant.stock_name}")
                return self._get_default_fitness_metrics()

            # Use the first available timeframe file
            feature_file = stock_files[0]
            df = pl.read_parquet(feature_file)

            # Prepare data for GPU processing
            gpu_data = gpu_accelerator.prepare_data_for_gpu(df)
            if not gpu_data:
                logger.warning(f"Failed to prepare GPU data for {strategy_variant.stock_name}")
                return self._get_default_fitness_metrics()

            # Convert strategy variant to GPU-compatible format
            strategy_config = {
                'name': strategy_variant.base_strategy_name,
                'type': strategy_variant.base_strategy_name,  # Use strategy name as type
                'oversold_threshold': strategy_variant.entry_conditions.get('oversold_threshold', 30),
                'overbought_threshold': strategy_variant.entry_conditions.get('overbought_threshold', 70),
                'lookback': strategy_variant.entry_conditions.get('lookback', 10),
                'threshold': strategy_variant.entry_conditions.get('threshold', 0.02)
            }

            # Run GPU-accelerated backtesting
            gpu_results = gpu_accelerator.vectorized_backtest_gpu(
                gpu_data,
                [strategy_config],
                strategy_variant.stock_name,
                strategy_variant.timeframe
            )

            if not gpu_results:
                logger.warning(f"GPU backtesting failed for {strategy_variant.stock_name}")
                return self._get_default_fitness_metrics()

            # Extract metrics from GPU result
            gpu_result = gpu_results[0]
            fitness_metrics = {
                'sharpe_ratio': gpu_result.sharpe_ratio,
                'max_drawdown': abs(gpu_result.max_drawdown),
                'win_rate': gpu_result.win_rate,
                'total_trades': gpu_result.total_trades,
                'total_pnl': gpu_result.total_pnl,
                'roi': gpu_result.roi
            }

            # Calculate composite fitness score
            composite_score = self._calculate_composite_fitness(fitness_metrics)
            fitness_metrics['composite_score'] = composite_score

            # Update strategy variant
            strategy_variant.performance_metrics = fitness_metrics
            strategy_variant.last_updated = datetime.now()

            # Clean up GPU memory
            gpu_accelerator.cleanup_gpu_memory()

            return fitness_metrics

        except Exception as e:
            logger.error(f"GPU fitness evaluation failed: {e}")
            gpu_accelerator.cleanup_gpu_memory()
            return self._get_default_fitness_metrics()

    async def evaluate_strategy_fitness_batch_gpu(self, strategy_variants: List[StrategyVariant]) -> Dict[str, Dict[str, float]]:
        """
        GPU-accelerated batch evaluation of multiple strategy variants

        This leverages GPU parallelism for maximum performance
        """
        try:
            if not strategy_variants:
                return {}

            # Group variants by stock for efficient GPU processing
            stock_groups = {}
            for variant in strategy_variants:
                stock_name = variant.stock_name
                if stock_name not in stock_groups:
                    stock_groups[stock_name] = []
                stock_groups[stock_name].append(variant)

            results = {}

            # Process each stock group on GPU
            for stock_name, variants in stock_groups.items():
                try:
                    # Load feature data once per stock
                    stock_files = list(Path("data/features").glob(f"features_{stock_name}_*.parquet"))
                    if not stock_files:
                        logger.warning(f"No feature data found for {stock_name}")
                        for variant in variants:
                            results[variant.strategy_id] = self._get_default_fitness_metrics()
                        continue

                    df = pl.read_parquet(stock_files[0])
                    gpu_data = gpu_accelerator.prepare_data_for_gpu(df)

                    if not gpu_data:
                        logger.warning(f"Failed to prepare GPU data for {stock_name}")
                        for variant in variants:
                            results[variant.strategy_id] = self._get_default_fitness_metrics()
                        continue

                    # Convert all variants to GPU-compatible strategies
                    gpu_strategies = []
                    for variant in variants:
                        strategy_config = {
                            'name': variant.base_strategy_name,
                            'type': variant.base_strategy_name,
                            'oversold_threshold': variant.entry_conditions.get('oversold_threshold', 30),
                            'overbought_threshold': variant.entry_conditions.get('overbought_threshold', 70),
                            'lookback': variant.entry_conditions.get('lookback', 10),
                            'threshold': variant.entry_conditions.get('threshold', 0.02)
                        }
                        gpu_strategies.append(strategy_config)

                    # Run GPU batch backtesting
                    gpu_results = gpu_accelerator.vectorized_backtest_gpu(
                        gpu_data, gpu_strategies, stock_name, variants[0].timeframe
                    )

                    # Process results
                    for i, (variant, gpu_result) in enumerate(zip(variants, gpu_results)):
                        fitness_metrics = {
                            'sharpe_ratio': gpu_result.sharpe_ratio,
                            'max_drawdown': abs(gpu_result.max_drawdown),
                            'win_rate': gpu_result.win_rate,
                            'total_trades': gpu_result.total_trades,
                            'total_pnl': gpu_result.total_pnl,
                            'roi': gpu_result.roi
                        }

                        composite_score = self._calculate_composite_fitness(fitness_metrics)
                        fitness_metrics['composite_score'] = composite_score

                        # Update variant
                        variant.performance_metrics = fitness_metrics
                        variant.last_updated = datetime.now()

                        results[variant.strategy_id] = fitness_metrics

                except Exception as e:
                    logger.error(f"GPU batch processing failed for {stock_name}: {e}")
                    for variant in variants:
                        results[variant.strategy_id] = self._get_default_fitness_metrics()

            # Clean up GPU memory
            gpu_accelerator.cleanup_gpu_memory()

            logger.info(f"🎯 GPU batch evaluation completed for {len(results)} variants")
            return results

        except Exception as e:
            logger.error(f"GPU batch evaluation failed: {e}")
            gpu_accelerator.cleanup_gpu_memory()
            return {variant.strategy_id: self._get_default_fitness_metrics() for variant in strategy_variants}

    async def run_multi_objective_optimization(self, base_strategy: Dict[str, Any],
                                             stock_name: str, timeframe: str) -> List[StrategyVariant]:
        """
        Run multi-objective optimization for a strategy-stock combination

        This addresses Enhancement Point #3: Multi-Objective Optimization
        """
        try:
            # Update progress tracking
            self.evolution_stats['current_stock'] = stock_name
            self.evolution_stats['current_strategy'] = base_strategy['name']

            # Show stock-specific progress
            logger.stock_progress(stock_name, f"Testing strategy {base_strategy['name']} ({timeframe})")

            # Multi-objective optimization using Optuna

            # Create Optuna study with multiple objectives
            study_name = f"{base_strategy['name']}_{stock_name}_{timeframe}"
            study = optuna.create_study(
                directions=["maximize", "minimize", "maximize"],  # sharpe_ratio, max_drawdown, win_rate
                study_name=study_name,
                storage=None  # In-memory for now
            )

            # Log study creation (one of the few messages we want to show)
            log_study_creation(study_name)

            # Define optimization objective function (synchronous wrapper for async call)
            def objective(trial):
                # Create strategy variant with trial parameters
                variant = self._create_variant_from_trial(trial, base_strategy, stock_name, timeframe)

                # Use synchronous fitness evaluation
                fitness_metrics = self._evaluate_strategy_fitness_sync(variant)

                # Return tuple of objectives (sharpe_ratio, max_drawdown, win_rate)
                return (
                    fitness_metrics.get('sharpe_ratio', 0.0),
                    fitness_metrics.get('max_drawdown', 100.0),
                    fitness_metrics.get('win_rate', 0.0)
                )

            # Run optimization (reduced for faster testing)
            n_trials = 5  # Reduced from 20 to 5 for faster testing
            study.optimize(objective, n_trials=n_trials)

            # Log trial summary (reduced logging)
            best_values = [trial.values for trial in study.best_trials[:3]] if study.best_trials else None
            log_trial_summary(n_trials, best_values)

            # Extract Pareto-optimal solutions
            pareto_trials = study.best_trials

            # Convert trials to strategy variants
            candidate_variants = []
            for trial in pareto_trials[:self.evolution_config.max_variants_per_strategy]:
                variant = self._create_variant_from_trial(trial, base_strategy, stock_name, timeframe)
                candidate_variants.append(variant)

            # Use fast mock evaluation during optimization (for speed)
            # Full backtesting will be done later for final best variants
            if candidate_variants:
                optimized_variants = []
                for variant in candidate_variants:
                    # Use mock fitness evaluation for speed during optimization
                    fitness_metrics = self._evaluate_strategy_fitness_sync(variant)
                    variant.ranking = int(fitness_metrics['composite_score'] * 100)
                    variant.performance_metrics = fitness_metrics

                    # Only keep variants above threshold
                    if variant.ranking >= self.evolution_config.min_ranking_threshold:
                        optimized_variants.append(variant)

                # Log progress for this stock
                logger.stock_progress(stock_name, f"Generated {len(optimized_variants)}/{len(candidate_variants)} variants above threshold")

                # Update evolution stats
                self.evolution_stats['variants_generated'] += len(candidate_variants)
                self.evolution_stats['variants_above_threshold'] += len(optimized_variants)
                self.evolution_stats['optimization_tasks_completed'] += 1
            else:
                optimized_variants = []

            logger.info(f"✅ Generated {len(optimized_variants)} optimized variants")
            return optimized_variants

        except Exception as e:
            logger.error(f"Error in multi-objective optimization: {e}")
            self.evolution_stats['optimization_tasks_failed'] += 1
            return []

    def _create_variant_from_trial(self, trial, base_strategy: Dict[str, Any],
                                 stock_name: str, timeframe: str) -> StrategyVariant:
        """Create strategy variant from Optuna trial parameters using proper strategy structure"""
        try:
            # Sample parameters for optimization
            risk_reward_ratio = [
                trial.suggest_float('risk_pct', 0.5, 3.0),
                trial.suggest_float('reward_pct', 1.0, 5.0)
            ]

            stop_loss_value = trial.suggest_float('stop_loss', 0.005, 0.03)
            take_profit_value = risk_reward_ratio[1] / risk_reward_ratio[0] * stop_loss_value

            # Extract proper entry/exit conditions from base strategy
            entry_conditions = {
                'long': base_strategy.get('entry', {}).get('long', 'rsi_14 < 30'),
                'short': base_strategy.get('entry', {}).get('short', 'rsi_14 > 70')
            }
            
            exit_conditions = {
                'long_exit': base_strategy.get('exit', {}).get('long', 'rsi_14 > 70'),
                'short_exit': base_strategy.get('exit', {}).get('short', 'rsi_14 < 30')
            }

            # Create variant with optimized parameters
            variant = StrategyVariant(
                strategy_id=str(uuid.uuid4()),
                base_strategy_name=base_strategy['name'],
                stock_name=stock_name,
                timeframe=timeframe,
                ranking=100,  # Will be updated after fitness evaluation
                entry_conditions=entry_conditions,
                exit_conditions=exit_conditions,
                intraday_rules=base_strategy.get('intraday_rules', {
                    'no_trades_after': '15:00',
                    'square_off_time': '15:20'
                }),
                risk_reward_ratios=[risk_reward_ratio],
                risk_management={
                    'stop_loss_type': 'percentage',
                    'stop_loss_value': stop_loss_value,
                    'take_profit_type': 'percentage',
                    'take_profit_value': take_profit_value
                },
                position_sizing=base_strategy.get('position_sizing', {
                    'method': 'fixed_amount',
                    'amount': 10000
                })
            )

            return variant

        except Exception as e:
            logger.error(f"Error creating variant from trial: {e}")
            return None

    async def enhance_strategies_yaml(self, infinite_mode: bool = False) -> bool:
        """
        Enhance strategies.yaml with stock-specific variants

        This addresses the main goal: enhancing strategies.yaml with best-performing stocks
        """
        try:
            logger.info("🚀 Starting strategy enhancement process")

            if infinite_mode:
                return await self._run_infinite_evolution()
            else:
                return await self._run_single_evolution()

        except Exception as e:
            logger.error(f"Strategy enhancement failed: {e}")
            return False

    async def _run_infinite_evolution(self) -> bool:
        """Run continuous evolution with genetic algorithm"""
        logger.info("🔄 Starting infinite evolution mode with genetic algorithm")

        self.is_running = True

        try:
            logger.info("[INFINITE_EVO] Entering infinite evolution loop.")
            while self.is_running:
                self.generation_counter += 1
                logger.evolution_progress(self.generation_counter, "Starting evolution cycle")

                # Run single evolution cycle
                success = await self._run_single_evolution()

                if success:
                    logger.evolution_progress(self.generation_counter, "Completed successfully")
                else:
                    logger.evolution_progress(self.generation_counter, "Completed with issues")

                # Sleep between generations to prevent overheating
                print("😴 Resting for 60 seconds before next generation...")
                await asyncio.sleep(60)  # 1 minute pause between generations

        except KeyboardInterrupt:
            logger.info("🛑 Evolution stopped by user")
            self.is_running = False
        except Exception as e:
            logger.error(f"Infinite evolution error: {e}")
            self.is_running = False

        return True

    async def _run_single_evolution(self) -> bool:
        """Run single evolution cycle (original behavior)"""
        try:
            logger.info("🚀 Starting single evolution cycle")

            # Initialize evolution stats
            self.evolution_stats['start_time'] = datetime.now()
            self.evolution_stats['stocks_tested'] = 0
            self.evolution_stats['strategies_processed'] = 0

            # Load base strategies and discover stock universe
            if not await self.load_base_strategies():
                return False

            stock_universe = await self.discover_stock_universe()
            if not stock_universe:
                logger.error("No stocks found in universe")
                return False

            # Update stats
            self.evolution_stats['stocks_tested'] = len(stock_universe)

            # Generate enhanced strategies
            enhanced_strategies = []

            # Process strategies in parallel for better performance
            strategy_tasks = []

            for strategy_idx, base_strategy in enumerate(self.base_strategies):
                strategy_name = base_strategy.get('name')
                print(f"🧬 Processing strategy {strategy_idx + 1}/{len(self.base_strategies)}: {strategy_name}")
                logger.info(f"[EVO_CYCLE] Processing base strategy: {strategy_name}")

                # Update stats
                self.evolution_stats['strategies_processed'] += 1

                # Create optimization tasks for parallel execution
                optimization_tasks = []
                # Process ALL stocks in universe (removed artificial limit)
                for stock_name in stock_universe:
                    for timeframe in base_strategy.get('timeframe', ['1min']):
                        task = self.run_multi_objective_optimization(base_strategy, stock_name, timeframe)
                        optimization_tasks.append(task)

                # Execute optimization tasks in parallel with controlled concurrency
                if optimization_tasks:
                    print(f"⚡ Running {len(optimization_tasks)} optimization tasks for {strategy_name}")
                    logger.info(f"[EVO_CYCLE] Executing {len(optimization_tasks)} optimization tasks concurrently.")
                    semaphore = asyncio.Semaphore(2)  # Reduced to 2 for better stability

                    completed_tasks = 0
                    total_tasks = len(optimization_tasks)

                    async def run_with_semaphore(task):
                        nonlocal completed_tasks
                        async with semaphore:
                            result = await task
                            completed_tasks += 1

                            # Show progress every 10 completed tasks
                            if completed_tasks % 10 == 0 or completed_tasks == total_tasks:
                                progress_pct = (completed_tasks / total_tasks) * 100
                                print(f"📈 Progress: {completed_tasks}/{total_tasks} ({progress_pct:.1f}%) - {strategy_name}")

                            return result

                    # Execute all tasks concurrently with timeout
                    try:
                        optimization_results = await asyncio.wait_for(
                            asyncio.gather(
                                *[run_with_semaphore(task) for task in optimization_tasks],
                                return_exceptions=True
                            ),
                            timeout=300  # 5 minute timeout per strategy
                        )
                    except asyncio.TimeoutError:
                        print(f"⚠️ Timeout processing {strategy_name} - moving to next strategy")
                        logger.warning(f"Timeout processing strategy {strategy_name}")
                        continue

                    # Collect results
                    strategy_variants = []
                    for result in optimization_results:
                        if isinstance(result, list):
                            strategy_variants.extend(result)
                        elif isinstance(result, Exception):
                            logger.error(f"Optimization task failed: {result}")
                            continue

                    # Select best variants for this strategy
                    if strategy_variants:
                        # Sort by ranking (descending)
                        strategy_variants.sort(key=lambda x: x.ranking, reverse=True)

                        # Take top variants
                        top_variants = strategy_variants[:self.evolution_config.max_variants_per_strategy]

                        # Convert to enhanced strategy format
                        for variant in top_variants:
                            enhanced_strategy = self._variant_to_yaml_format(variant)
                            enhanced_strategies.append(enhanced_strategy)

                            # Store in database
                            await self._save_variant_to_database(variant)

            # Update strategies.yaml with enhanced strategies
            await self._update_strategies_yaml(enhanced_strategies)

            # Update final stats
            self.evolution_stats['variants_added_to_yaml'] = len(enhanced_strategies)

            # Print comprehensive summary
            self._print_evolution_summary()

            print(f"✅ Evolution cycle completed - {len(enhanced_strategies)} variants added to strategies.yaml")
            logger.info(f"✅ Enhanced strategies.yaml with {len(enhanced_strategies)} variants")
            return True

        except Exception as e:
            logger.error(f"Error enhancing strategies.yaml: {e}")
            return False

    def _variant_to_yaml_format(self, variant: StrategyVariant) -> Dict[str, Any]:
        """Convert strategy variant to YAML format for strategies.yaml with enhanced metadata"""
        # Calculate best risk-reward ratio from performance
        best_risk_reward = "1:2"  # Default
        if variant.risk_reward_ratios:
            # Find the ratio with best performance
            best_risk_reward = variant.risk_reward_ratios[0] if isinstance(variant.risk_reward_ratios, list) else str(variant.risk_reward_ratios)

        # Extract key performance metrics for display
        performance_summary = {}
        if variant.performance_metrics:
            try:
                metrics = json.loads(variant.performance_metrics) if isinstance(variant.performance_metrics, str) else variant.performance_metrics
                performance_summary = {
                    'sharpe_ratio': round(metrics.get('sharpe_ratio', 0), 3),
                    'max_drawdown': round(metrics.get('max_drawdown', 0), 2),
                    'win_rate': round(metrics.get('win_rate', 0), 3),
                    'total_trades': int(metrics.get('total_trades', 0))
                }
            except json.JSONDecodeError as e:
                logger.error(f"JSONDecodeError in _variant_to_yaml_format for strategy {variant.strategy_id}: {e}. Raw metrics: {variant.performance_metrics}")
            except Exception as e:
                logger.error(f"Error processing performance_metrics in _variant_to_yaml_format for strategy {variant.strategy_id}: {e}. Raw metrics: {variant.performance_metrics}")

        return {
            'name': f"{variant.base_strategy_name}_{variant.stock_name}_{variant.timeframe}",
            'ranking': int(variant.ranking),
            'timeframe': [variant.timeframe],
            'stock_name': variant.stock_name,
            'best_risk_reward': best_risk_reward,
            'performance_summary': performance_summary,
            'confidence_score': round(variant.confidence_score, 3) if variant.confidence_score else 0.0,
            'market_regime': variant.market_regime or 'neutral',
            'last_updated': variant.last_updated.strftime('%Y-%m-%d %H:%M:%S') if variant.last_updated else datetime.now().strftime('%Y-%m-%d %H:%M:%S'),
            'entry': variant.entry_conditions,
            'exit': variant.exit_conditions,
            'intraday_rules': variant.intraday_rules,
            'risk_reward_ratios': variant.risk_reward_ratios,
            'risk_management': variant.risk_management,
            'position_sizing': variant.position_sizing
        }

    async def _save_variant_to_database(self, variant: StrategyVariant):
        """Save strategy variant to database"""
        try:
            conn = sqlite3.connect(self.database_path)
            cursor = conn.cursor()

            cursor.execute('''
                INSERT OR REPLACE INTO strategy_variants (
                    strategy_id, base_strategy_name, stock_name, timeframe, ranking,
                    entry_conditions, exit_conditions, intraday_rules, risk_reward_ratios,
                    risk_management, position_sizing, performance_metrics, status,
                    creation_date, last_updated, market_regime, confidence_score
                ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
            ''', (
                variant.strategy_id,
                variant.base_strategy_name,
                variant.stock_name,
                variant.timeframe,
                variant.ranking,
                json.dumps(variant.entry_conditions),
                json.dumps(variant.exit_conditions),
                json.dumps(variant.intraday_rules),
                json.dumps(variant.risk_reward_ratios),
                json.dumps(variant.risk_management),
                json.dumps(variant.position_sizing),
                json.dumps(variant.performance_metrics),
                variant.status.value,
                variant.creation_date.isoformat(),
                variant.last_updated.isoformat(),
                variant.market_regime.value if variant.market_regime else None,
                variant.confidence_score
            ))

            conn.commit()
            conn.close()

        except Exception as e:
            logger.error(f"Error saving variant to database: {e}")

    async def _update_strategies_yaml(self, enhanced_strategies: List[Dict[str, Any]]):
        """Update strategies.yaml with enhanced strategies"""
        try:
            strategies_path = "config/strategies.yaml"

            # Load existing strategies
            with open(strategies_path, 'r', encoding='utf-8') as file:
                data = yaml.safe_load(file)

            existing_strategies = data.get('strategies', [])

            # Create backup with rotation (keep only last 5 backups)
            backup_path = self._create_rotated_backup(data)
            logger.info(f"📋 Created backup at {backup_path}")

            # Add enhanced strategies (avoid duplicates)
            existing_names = {s.get('name', '') for s in existing_strategies}
            new_strategies = []

            for enhanced_strategy in enhanced_strategies:
                if enhanced_strategy['name'] not in existing_names:
                    new_strategies.append(enhanced_strategy)

            # Update the strategies list
            all_strategies = existing_strategies + new_strategies

            # Sort by ranking (descending)
            all_strategies.sort(key=lambda x: x.get('ranking', 0), reverse=True)

            # Update data
            data['strategies'] = all_strategies

            # Write updated strategies.yaml
            with open(strategies_path, 'w', encoding='utf-8') as file:
                yaml.dump(data, file, default_flow_style=False, indent=2)

            logger.info(f"✅ Updated strategies.yaml with {len(new_strategies)} new enhanced strategies")

        except Exception as e:
            logger.error(f"Error updating strategies.yaml: {e}")

    async def load_variants_from_database(self) -> List[StrategyVariant]:
        """Load strategy variants from database"""
        try:
            conn = sqlite3.connect(self.database_path)
            cursor = conn.cursor()

            cursor.execute('SELECT * FROM strategy_variants ORDER BY ranking DESC')
            rows = cursor.fetchall()

            variants = []
            for row in rows:
                try:
                    variant = StrategyVariant(
                        strategy_id=row[0],
                        base_strategy_name=row[1],
                        stock_name=row[2],
                        timeframe=row[3],
                        ranking=row[4],
                        entry_conditions=json.loads(row[5]),
                        exit_conditions=json.loads(row[6]),
                        intraday_rules=json.loads(row[7]),
                        risk_reward_ratios=json.loads(row[8]),
                        risk_management=json.loads(row[9]),
                        position_sizing=json.loads(row[10]),
                        performance_metrics=json.loads(row[11]) if row[11] else {},
                        status=StrategyStatus(row[12]),
                        creation_date=datetime.fromisoformat(row[13]),
                        last_updated=datetime.fromisoformat(row[14]),
                        market_regime=MarketRegime(row[15]) if row[15] else None,
                        confidence_score=row[16]
                    )
                    variants.append(variant)
                except json.JSONDecodeError as e:
                    logger.error(f"JSONDecodeError loading variant from DB (ID: {row[0]}): {e}. Raw performance_metrics: {row[11]}")
                except Exception as e:
                    logger.error(f"Error loading variant from DB (ID: {row[0]}): {e}. Raw row data: {row}")

            conn.close()

            logger.info(f"📊 Loaded {len(variants)} variants from database")
            return variants

        except Exception as e:
            logger.error(f"Error loading variants from database: {e}")
            return []

    async def manage_strategy_lifecycle(self):
        """
        Manage strategy lifecycle with promotion/demotion logic

        This addresses Enhancement Point #9: Strategy Lifecycle Management
        """
        try:
            logger.info("🔄 Managing strategy lifecycle")

            # Load all variants from database
            variants = await self.load_variants_from_database()

            for variant in variants:
                # Evaluate current performance
                current_metrics = await self.evaluate_strategy_fitness(variant)

                # Update performance history
                await self._save_performance_history(variant.strategy_id, current_metrics)

                # Determine status based on performance
                new_status = self._determine_strategy_status(variant, current_metrics)

                if new_status != variant.status:
                    logger.info(f"📈 Status change for {variant.strategy_id}: {variant.status.value} -> {new_status.value}")
                    variant.status = new_status
                    variant.last_updated = datetime.now()

                    # Update database
                    await self._save_variant_to_database(variant)

            logger.info("✅ Strategy lifecycle management complete")

        except Exception as e:
            logger.error(f"Error managing strategy lifecycle: {e}")

    def _determine_strategy_status(self, variant: StrategyVariant,
                                 current_metrics: Dict[str, float]) -> StrategyStatus:
        """Determine strategy status based on performance"""
        try:
            composite_score = current_metrics.get('composite_score', 0.0)

            # Status promotion/demotion logic
            if composite_score >= 0.8:
                return StrategyStatus.CHAMPION
            elif composite_score >= 0.6:
                return StrategyStatus.CHALLENGER
            elif composite_score >= 0.4:
                return StrategyStatus.TESTING
            elif composite_score >= 0.2:
                return StrategyStatus.CANDIDATE
            else:
                return StrategyStatus.FAILED

        except Exception as e:
            logger.error(f"Error determining strategy status: {e}")
            return StrategyStatus.CANDIDATE

    async def _save_performance_history(self, strategy_id: str, metrics: Dict[str, float]):
        """Save performance history to database"""
        try:
            conn = sqlite3.connect(self.database_path)
            cursor = conn.cursor()

            cursor.execute('''
                INSERT INTO performance_history (strategy_id, timestamp, metrics, market_regime)
                VALUES (?, ?, ?, ?)
            ''', (
                strategy_id,
                datetime.now().isoformat(),
                json.dumps(metrics),
                None  # Market regime detection would go here
            ))

            conn.commit()
            conn.close()

        except Exception as e:
            logger.error(f"Error saving performance history: {e}")

    async def start_evolution_process(self) -> bool:
        """Start the main evolution process"""
        try:
            logger.info("🚀 Starting Enhanced Strategy Evolution Process")

            self.is_running = True

            # Main evolution loop
            while self.is_running and self.generation_counter < self.evolution_config.max_generations:
                logger.info(f"🧬 Generation {self.generation_counter + 1}")

                # Enhance strategies.yaml with optimized variants
                success = await self.enhance_strategies_yaml()

                if not success:
                    logger.error("Strategy enhancement failed")
                    break

                # Manage strategy lifecycle
                await self.manage_strategy_lifecycle()

                # Increment generation
                self.generation_counter += 1

                # Wait before next generation (configurable)
                await asyncio.sleep(3600)  # 1 hour between generations

            logger.info("✅ Evolution process completed")
            return True

        except Exception as e:
            logger.error(f"Error in evolution process: {e}")
            return False

    def stop_evolution_process(self):
        """Stop the evolution process"""
        self.is_running = False
        logger.info("🛑 Evolution process stopped")

# Example usage and testing
async def main():
    """Main function for testing the Enhanced Strategy Evolution Agent"""
    try:
        # Initialize agent
        agent = EnhancedStrategyEvolutionAgent()

        # Run single enhancement cycle
        success = await agent.enhance_strategies_yaml()

        if success:
            logger.info("✅ Strategy enhancement completed successfully")
        else:
            logger.error("❌ Strategy enhancement failed")

    except Exception as e:
        logger.error(f"Error in main: {e}")

if __name__ == "__main__":
    asyncio.run(main())
